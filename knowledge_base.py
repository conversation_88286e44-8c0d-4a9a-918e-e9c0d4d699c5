import os
import json
import re
import pickle
from typing import List, Dict, Tu<PERSON>, Optional
import docx
import requests
import numpy as np

# 延迟导入ChromaDB相关模块，避免在安装过程中出错
try:
    import chromadb
    from chromadb.config import Settings
    #from sentence_transformers import SentenceTransformer
    CHROMADB_AVAILABLE = True
except ImportError:
    CHROMADB_AVAILABLE = False
    print("ChromaDB或SentenceTransformers未安装，使用TF-IDF备选方案")

# 备选方案：TF-IDF
try:
    from sklearn.feature_extraction.text import TfidfVectorizer
    from sklearn.metrics.pairwise import cosine_similarity
    import jieba
    TFIDF_AVAILABLE = True
except ImportError:
    TFIDF_AVAILABLE = False
from config import (
    MARKDOWN_MANUAL_PATH,
    SQL_FILE_PATH,
    KNOWLEDGE_BASE_ENABLED,
    KNOWLEDGE_BASE_TOP_K,
    KN<PERSON>LEDGE_BASE_SIMILARITY_THRESHOLD,
    K<PERSON><PERSON>LEDGE_BASE_CACHE_DIR,
    EMBEDDING_MODEL,
    EMBEDDING_API_BASE,
    EMBEDDING_API_KEY,
    CHUNK_PROCESSING_ENABLED,
    MARKDOWN_CHUNK_BY_H1,
    MARKDOWN_CHUNK_PATTERN,
    SQL_CHUNK_BY_TABLE,
    MAX_CHUNK_SIZE,
    MIN_CHUNK_SIZE,
    EMBEDDING_BATCH_SIZE
)


class OpenAIEmbedding:
    """OpenAI嵌入模型包装类"""

    def __init__(self, model_name: str, api_base: str, api_key: str):
        self.model_name = model_name
        self.api_key = api_key

        # 构建嵌入API端点        
        base_url = api_base.rstrip('/')
        self.api_base = base_url + "/" + self.model_name

    def encode(self, texts: List[str], show_progress_bar: bool = False, batch_size: int = None) -> np.ndarray:
        """
        对文本进行编码，返回嵌入向量

        Args:
            texts: 要编码的文本列表
            show_progress_bar: 是否显示进度条（兼容性参数）
            batch_size: 批次大小，如果为None则使用默认值

        Returns:
            嵌入向量数组
        """
        if show_progress_bar:
            print(f"正在编码 {len(texts)} 个文本...")

        embeddings = []
        # 使用传入的batch_size或默认值，但不超过10以避免payload过大
        actual_batch_size = batch_size if batch_size is not None else 10
        # 确保批次大小不超过10
        actual_batch_size = min(actual_batch_size, 10)

        for i in range(0, len(texts), actual_batch_size):
            batch_texts = texts[i:i + actual_batch_size]
            batch_embeddings = self._get_embeddings(batch_texts)

            if batch_embeddings is None:
                # API调用失败，返回None
                return None

            embeddings.extend(batch_embeddings)

            if show_progress_bar and len(texts) > actual_batch_size:
                print(f"已完成 {min(i + actual_batch_size, len(texts))}/{len(texts)}")

        return np.array(embeddings)

    def _get_embeddings(self, texts: List[str]) -> List[List[float]]:
        """
        调用OpenAI API获取嵌入向量

        Args:
            texts: 文本列表

        Returns:
            嵌入向量列表
        """
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}"
        }

        data = {
            "model": self.model_name,
            "inputs": texts
        }

        try:
            response = requests.post(
                self.api_base,
                headers=headers,
                json=data,
                timeout=60
            )
            response.raise_for_status()

            result = response.json()

            # 提取嵌入向量
            embeddings = []
            # 检查result本身是否List，并添加到embeddings列表中
            if isinstance(result, list):
                embeddings.extend(result)
            else:
                for item in result.get('data', []):
                    embeddings.append(item.get('embedding', []))

            return embeddings

        except requests.exceptions.RequestException as e:
            print(f"调用嵌入API失败: {e}")
            # 返回None表示失败
            return None
        except Exception as e:
            print(f"解析嵌入响应失败: {e}")
            return None


class KnowledgeBase:
    """知识库管理类，负责文档解析、向量化存储和检索"""

    def __init__(self):
        self.enabled = KNOWLEDGE_BASE_ENABLED and (CHROMADB_AVAILABLE or TFIDF_AVAILABLE)
        self.top_k = KNOWLEDGE_BASE_TOP_K
        self.similarity_threshold = KNOWLEDGE_BASE_SIMILARITY_THRESHOLD
        self.cache_dir = KNOWLEDGE_BASE_CACHE_DIR
        self.embedding_model_name = EMBEDDING_MODEL

        # 分块处理配置
        self.chunk_processing_enabled = CHUNK_PROCESSING_ENABLED
        self.markdown_chunk_by_h1 = MARKDOWN_CHUNK_BY_H1
        self.markdown_chunk_pattern = MARKDOWN_CHUNK_PATTERN
        self.sql_chunk_by_table = SQL_CHUNK_BY_TABLE
        self.max_chunk_size = MAX_CHUNK_SIZE
        self.min_chunk_size = MIN_CHUNK_SIZE
        self.embedding_batch_size = EMBEDDING_BATCH_SIZE

        # 确保缓存目录存在
        os.makedirs(self.cache_dir, exist_ok=True)

        # 知识库数据结构
        self.function_docs = []  # 功能说明文档
        self.entity_docs = []    # 数据实体文档

        # ChromaDB相关
        self.chroma_client = None
        self.function_collection = None
        self.entity_collection = None
        self.embedding_model = None

        # TF-IDF备选方案相关
        self.vectorizer = None
        self.function_vectors = None
        self.entity_vectors = None
        self.use_chromadb = CHROMADB_AVAILABLE

        # 检查依赖
        if not CHROMADB_AVAILABLE and not TFIDF_AVAILABLE:
            print("知识库依赖未安装，知识库功能已禁用")
            self.enabled = False
            return

        if not CHROMADB_AVAILABLE:
            print("使用TF-IDF备选方案")
            self.use_chromadb = False

        # 初始化知识库
        if self.enabled:
            self._initialize_knowledge_base()
    
    def _initialize_knowledge_base(self):
        """初始化知识库，加载或创建向量化数据"""
        try:
            if self.use_chromadb:
                self._initialize_chromadb()
            else:
                self._initialize_tfidf()
        except Exception as e:
            print(f"初始化知识库失败: {e}")
            self.enabled = False

    def _initialize_chromadb(self):
        """初始化ChromaDB"""
        try:
            # 初始化ChromaDB客户端
            self.chroma_client = chromadb.PersistentClient(
                path=os.path.join(self.cache_dir, "chroma_db"),
                settings=Settings(anonymized_telemetry=False)
            )

            # 初始化嵌入模型
            print(f"正在初始化OpenAI嵌入模型: {self.embedding_model_name}")
            self.embedding_model = OpenAIEmbedding(
                model_name=self.embedding_model_name,
                api_base=EMBEDDING_API_BASE,
                api_key=EMBEDDING_API_KEY
            )

            # 测试嵌入模型是否可用
            # test_result = self.embedding_model.encode(["测试"])
            # if test_result is None or len(test_result) == 0 or all(x == 0 for x in test_result[0]):
            #     print("OpenAI嵌入模型不可用，回退到TF-IDF模式")
            #     self.use_chromadb = False
            #     self._initialize_tfidf()
            #     return

            # print("OpenAI嵌入模型初始化成功")

            # 获取或创建集合
            self.function_collection = self.chroma_client.get_or_create_collection(
                name="function_docs",
                metadata={"description": "功能说明文档"}
            )

            self.entity_collection = self.chroma_client.get_or_create_collection(
                name="entity_docs",
                metadata={"description": "数据实体文档"}
            )

            # 检查是否需要重新构建知识库
            if self.function_collection.count() == 0 or self.entity_collection.count() == 0:
                print("开始构建知识库...")
                self._build_knowledge_base()
            else:
                print("知识库已存在，跳过构建")
                # 加载文档数据用于显示
                self._load_docs_from_cache()

        except Exception as e:
            print(f"ChromaDB初始化失败: {e}")
            print("回退到TF-IDF模式")
            self.use_chromadb = False
            self._initialize_tfidf()

    def _initialize_tfidf(self):
        """初始化TF-IDF备选方案"""
        cache_file = os.path.join(self.cache_dir, "tfidf_cache.pkl")

        # 检查是否有缓存
        if os.path.exists(cache_file):
            try:
                with open(cache_file, 'rb') as f:
                    cache_data = pickle.load(f)
                    self.function_docs = cache_data.get('function_docs', [])
                    self.entity_docs = cache_data.get('entity_docs', [])
                    self.vectorizer = cache_data.get('vectorizer')
                    self.function_vectors = cache_data.get('function_vectors')
                    self.entity_vectors = cache_data.get('entity_vectors')
                print("TF-IDF知识库缓存加载成功")
                return
            except Exception as e:
                print(f"加载TF-IDF缓存失败: {e}")

        # 重新构建知识库
        print("开始构建TF-IDF知识库...")
        self._build_knowledge_base()
    
    def _build_knowledge_base(self):
        """构建知识库"""
        self.function_docs = []
        self.entity_docs = []

        # 解析用户手册 - 支持多个文件
        manual_paths = [path.strip() for path in MARKDOWN_MANUAL_PATH.split(',') if path.strip()]
        for manual_path in manual_paths:
            if os.path.exists(manual_path):
                docs = self._parse_user_manual(manual_path)
                self.function_docs.extend(docs)
                print(f"解析用户手册 {manual_path} 完成，提取功能说明 {len(docs)} 条")
            else:
                print(f"用户手册文件不存在: {manual_path}")

        # 解析SQL/JSON文件 - 支持多个文件和多种格式
        sql_paths = [path.strip() for path in SQL_FILE_PATH.split(',') if path.strip()]
        for sql_path in sql_paths:
            if os.path.exists(sql_path):
                if sql_path.endswith('.json'):
                    docs = self._parse_json_file(sql_path)
                    self.entity_docs.extend(docs)
                    print(f"解析JSON文件 {sql_path} 完成，提取数据实体 {len(docs)} 条")
                else:
                    docs = self._parse_sql_file(sql_path)
                    self.entity_docs.extend(docs)
                    print(f"解析SQL文件 {sql_path} 完成，提取数据实体 {len(docs)} 条")
            else:
                print(f"SQL/JSON文件不存在: {sql_path}")

        print(f"知识库构建完成，总计功能说明 {len(self.function_docs)} 条，数据实体 {len(self.entity_docs)} 条")

        # 存储到向量数据库
        if self.use_chromadb:
            self._store_to_chromadb()
        else:
            self._store_to_tfidf()

    def _load_docs_from_cache(self):
        """从缓存加载文档数据"""
        cache_file = os.path.join(self.cache_dir, "docs_cache.pkl")
        if os.path.exists(cache_file):
            try:
                with open(cache_file, 'rb') as f:
                    cache_data = pickle.load(f)
                    self.function_docs = cache_data.get('function_docs', [])
                    self.entity_docs = cache_data.get('entity_docs', [])
                print(f"从缓存加载文档: 功能说明 {len(self.function_docs)} 条，数据实体 {len(self.entity_docs)} 条")
            except Exception as e:
                print(f"加载文档缓存失败: {e}")

    def _store_to_chromadb(self):
        """将文档存储到ChromaDB"""
        try:
            # 清空现有集合（如果有数据的话）
            if self.function_collection.count() > 0:
                # 获取所有ID并删除
                all_func_data = self.function_collection.get()
                if all_func_data['ids']:
                    self.function_collection.delete(ids=all_func_data['ids'])

            if self.entity_collection.count() > 0:
                # 获取所有ID并删除
                all_entity_data = self.entity_collection.get()
                if all_entity_data['ids']:
                    self.entity_collection.delete(ids=all_entity_data['ids'])

            # 存储功能文档
            if self.function_docs:
                print("正在向量化功能文档...")
                function_texts = [doc['content'] for doc in self.function_docs]
                function_embeddings = self.embedding_model.encode(
                    function_texts,
                    show_progress_bar=True,
                    batch_size=self.embedding_batch_size
                )

                if function_embeddings is not None:
                    self.function_collection.add(
                        embeddings=function_embeddings.tolist(),
                        documents=function_texts,
                        metadatas=[{
                            'title': doc['title'],
                            'type': doc['type'],
                            'source': doc['source']
                        } for doc in self.function_docs],
                        ids=[f"func_{i}" for i in range(len(self.function_docs))]
                    )
                    print(f"功能文档存储完成: {len(self.function_docs)} 条")
                else:
                    print("功能文档向量化失败")

            # 存储实体文档
            if self.entity_docs:
                print("正在向量化实体文档...")
                entity_texts = [doc['content'] for doc in self.entity_docs]
                entity_embeddings = self.embedding_model.encode(
                    entity_texts,
                    show_progress_bar=True,
                    batch_size=self.embedding_batch_size
                )

                if entity_embeddings is not None:
                    self.entity_collection.add(
                        embeddings=entity_embeddings.tolist(),
                        documents=entity_texts,
                        metadatas=[{
                            'table_name': doc['table_name'],
                            'table_comment': doc.get('table_comment', ''),
                            'type': doc['type'],
                            'source': doc['source']
                        } for doc in self.entity_docs],
                        ids=[f"entity_{i}" for i in range(len(self.entity_docs))]
                    )
                    print(f"实体文档存储完成: {len(self.entity_docs)} 条")
                else:
                    print("实体文档向量化失败")

            # 保存文档缓存
            cache_file = os.path.join(self.cache_dir, "docs_cache.pkl")
            with open(cache_file, 'wb') as f:
                pickle.dump({
                    'function_docs': self.function_docs,
                    'entity_docs': self.entity_docs
                }, f)
            print("文档缓存保存成功")

        except Exception as e:
            print(f"存储到ChromaDB失败: {e}")
            import traceback
            traceback.print_exc()

    def _store_to_tfidf(self):
        """将文档存储到TF-IDF向量空间"""
        try:
            # 准备文本数据
            function_texts = [doc['content'] for doc in self.function_docs]
            entity_texts = [doc['content'] for doc in self.entity_docs]

            # 创建向量化器（不使用自定义tokenizer避免pickle问题）
            self.vectorizer = TfidfVectorizer(
                max_features=5000,
                ngram_range=(1, 2),
                lowercase=False
            )

            # 合并所有文档进行训练
            all_texts = function_texts + entity_texts
            if all_texts:
                self.vectorizer.fit(all_texts)

                # 向量化功能文档
                if function_texts:
                    self.function_vectors = self.vectorizer.transform(function_texts)
                    print(f"功能文档向量化完成: {len(function_texts)} 条")

                # 向量化实体文档
                if entity_texts:
                    self.entity_vectors = self.vectorizer.transform(entity_texts)
                    print(f"实体文档向量化完成: {len(entity_texts)} 条")

            # 保存缓存
            cache_file = os.path.join(self.cache_dir, "tfidf_cache.pkl")
            with open(cache_file, 'wb') as f:
                pickle.dump({
                    'function_docs': self.function_docs,
                    'entity_docs': self.entity_docs,
                    'vectorizer': self.vectorizer,
                    'function_vectors': self.function_vectors,
                    'entity_vectors': self.entity_vectors
                }, f)
            print("TF-IDF缓存保存成功")

        except Exception as e:
            print(f"存储到TF-IDF失败: {e}")
            import traceback
            traceback.print_exc()
    
    def _parse_user_manual(self, file_path: str) -> List[Dict]:
        """解析用户手册，提取功能说明"""
        function_docs = []

        try:
            if file_path.endswith('.docx'):
                function_docs = self._parse_docx_manual(file_path)
            elif file_path.endswith('.md'):
                function_docs = self._parse_markdown_manual(file_path)

        except Exception as e:
            print(f"解析用户手册失败: {e}")

        return function_docs

    def _parse_docx_manual(self, file_path: str) -> List[Dict]:
        """解析Word文档用户手册"""
        function_docs = []

        print(f"开始解析Word文档: {file_path}")
        doc = docx.Document(file_path)
        current_section = ""
        current_content = ""
        section_count = 0

        for paragraph in doc.paragraphs:
            text = paragraph.text.strip()
            if not text:
                continue

            # 检测标题（多种格式）
            is_heading = False

            # 方法1：检查样式名
            if paragraph.style.name.startswith('Heading'):
                is_heading = True

            # 方法2：检查数字开头的标题
            elif re.match(r'^\d+(\.\d+)*[\s\.]+', text):
                is_heading = True

            # 方法3：检查中文章节标题
            elif re.match(r'^第[一二三四五六七八九十\d]+[章节部分]', text):
                is_heading = True

            # 方法4：检查字体大小和加粗（如果可用）
            elif len(text) < 50 and (paragraph.runs and paragraph.runs[0].bold):
                is_heading = True

            if is_heading:
                # 保存前一个章节
                if current_section and current_content.strip():
                    # 如果启用分块处理，检查内容大小
                    if self.chunk_processing_enabled and len(current_content) > self.max_chunk_size:
                        chunks = self._split_large_content(current_content, current_section)
                        function_docs.extend(chunks)
                    else:
                        function_docs.append({
                            'title': current_section,
                            'content': current_content.strip(),
                            'type': 'function',
                            'source': 'user_manual'
                        })
                    section_count += 1

                current_section = text
                current_content = ""
                print(f"  发现章节: {text[:50]}...")
            else:
                current_content += text + "\n"

        # 保存最后一个章节
        if current_section and current_content.strip():
            if self.chunk_processing_enabled and len(current_content) > self.max_chunk_size:
                chunks = self._split_large_content(current_content, current_section)
                function_docs.extend(chunks)
            else:
                function_docs.append({
                    'title': current_section,
                    'content': current_content.strip(),
                    'type': 'function',
                    'source': 'user_manual'
                })
            section_count += 1

        print(f"Word文档解析完成，提取了 {section_count} 个章节")
        return function_docs

    def _parse_markdown_manual(self, file_path: str) -> List[Dict]:
        """解析Markdown文档用户手册"""
        function_docs = []

        print(f"开始解析Markdown文档: {file_path}")
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        if self.chunk_processing_enabled and self.markdown_chunk_by_h1:
            # 按一级标题（#）分块
            function_docs = self._chunk_markdown_by_h1(content, file_path)
        else:
            # 原有的按所有标题分割的方式
            sections = re.split(r'\n#+\s+', content)
            for i, section in enumerate(sections):
                if i == 0:  # 跳过第一个空段
                    continue

                lines = section.split('\n')
                title = lines[0] if lines else f"Section {i}"
                content_text = '\n'.join(lines[1:]) if len(lines) > 1 else ""

                if content_text.strip():
                    function_docs.append({
                        'title': title,
                        'content': content_text.strip(),
                        'type': 'function',
                        'source': 'user_manual'
                    })

        print(f"Markdown文档解析完成，提取了 {len(function_docs)} 个分块")
        return function_docs

    def _chunk_markdown_by_h1(self, content: str, file_path: str) -> List[Dict]:
        """按一级标题（#）分块处理Markdown文档"""
        function_docs = []

        # 使用正则表达式按一级标题分割
        # 匹配行首的 # 后跟空格的模式
        sections = re.split(self.markdown_chunk_pattern, content, flags=re.MULTILINE)

        for i, section in enumerate(sections):
            section = section.strip()
            if not section:
                continue

            # 第一个分块通常是标题前的内容（如版权声明等）
            if i == 0:
                if section:
                    function_docs.append({
                        'title': '文档前言',
                        'content': section,
                        'type': 'function',
                        'source': 'user_manual'
                    })
                continue

            # 分离标题和内容
            lines = section.split('\n', 1)
            title = lines[0].strip() if lines else f"Section {i}"
            content_text = lines[1].strip() if len(lines) > 1 else ""

            if not content_text:
                # 如果没有内容，可能是只有标题的情况，跳过或记录
                continue

            # 检查内容大小，如果太大则进一步分割
            if len(content_text) > self.max_chunk_size:
                print(f"  章节 '{title}' 内容过大 ({len(content_text)} 字符)，进行进一步分割")
                chunks = self._split_large_content(content_text, title)
                function_docs.extend(chunks)
            else:
                function_docs.append({
                    'title': title,
                    'content': content_text,
                    'type': 'function',
                    'source': 'user_manual'
                })
                print(f"  提取章节: {title} ({len(content_text)} 字符)")

        return function_docs

    def _split_large_content(self, content: str, title: str) -> List[Dict]:
        """将过大的内容分割成多个小块"""
        chunks = []

        # 尝试按段落分割
        paragraphs = content.split('\n\n')
        current_chunk = ""
        chunk_index = 1

        for paragraph in paragraphs:
            paragraph = paragraph.strip()
            if not paragraph:
                continue

            # 如果当前块加上新段落不会超过限制，则添加
            if len(current_chunk) + len(paragraph) + 2 <= self.max_chunk_size:
                if current_chunk:
                    current_chunk += "\n\n" + paragraph
                else:
                    current_chunk = paragraph
            else:
                # 保存当前块
                if current_chunk and len(current_chunk) >= self.min_chunk_size:
                    chunks.append({
                        'title': f"{title} (第{chunk_index}部分)",
                        'content': current_chunk,
                        'type': 'function',
                        'source': 'user_manual'
                    })
                    chunk_index += 1

                # 开始新块
                current_chunk = paragraph

                # 如果单个段落就超过了最大限制，需要按句子分割
                if len(current_chunk) > self.max_chunk_size:
                    sentence_chunks = self._split_by_sentences(current_chunk, title, chunk_index)
                    chunks.extend(sentence_chunks)
                    chunk_index += len(sentence_chunks)
                    current_chunk = ""

        # 保存最后一个块
        if current_chunk and len(current_chunk) >= self.min_chunk_size:
            chunks.append({
                'title': f"{title} (第{chunk_index}部分)",
                'content': current_chunk,
                'type': 'function',
                'source': 'user_manual'
            })

        print(f"    分割为 {len(chunks)} 个子块")
        return chunks

    def _split_by_sentences(self, content: str, title: str, start_index: int) -> List[Dict]:
        """按句子分割过大的段落"""
        chunks = []

        # 按句号、问号、感叹号分割
        sentences = re.split(r'[。！？\.\!\?]+', content)
        current_chunk = ""
        chunk_index = start_index

        for sentence in sentences:
            sentence = sentence.strip()
            if not sentence:
                continue

            if len(current_chunk) + len(sentence) + 1 <= self.max_chunk_size:
                if current_chunk:
                    current_chunk += sentence + "。"
                else:
                    current_chunk = sentence + "。"
            else:
                # 保存当前块
                if current_chunk and len(current_chunk) >= self.min_chunk_size:
                    chunks.append({
                        'title': f"{title} (第{chunk_index}部分)",
                        'content': current_chunk,
                        'type': 'function',
                        'source': 'user_manual'
                    })
                    chunk_index += 1

                # 开始新块
                current_chunk = sentence + "。"

        # 保存最后一个块
        if current_chunk and len(current_chunk) >= self.min_chunk_size:
            chunks.append({
                'title': f"{title} (第{chunk_index}部分)",
                'content': current_chunk,
                'type': 'function',
                'source': 'user_manual'
            })

        return chunks
    
    def _parse_sql_file(self, file_path: str) -> List[Dict]:
        """解析SQL文件，提取数据实体信息"""
        entity_docs = []

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                sql_content = f.read()

            print(f"SQL文件大小: {len(sql_content)} 字符")

            if self.chunk_processing_enabled and self.sql_chunk_by_table:
                # 按表分块处理
                entity_docs = self._chunk_sql_by_table(sql_content, file_path)
            else:
                # 原有的处理方式
                entity_docs = self._parse_sql_tables_legacy(sql_content)

        except Exception as e:
            print(f"解析SQL文件失败: {e}")
            import traceback
            traceback.print_exc()

        return entity_docs

    def _chunk_sql_by_table(self, sql_content: str, file_path: str) -> List[Dict]:
        """按数据表分块处理SQL文件"""
        entity_docs = []

        # 提取CREATE TABLE语句 - 修改正则表达式以适应PostgreSQL语法
        table_pattern = r'CREATE\s+TABLE\s+(?:IF\s+NOT\s+EXISTS\s+)?"?(\w+)"?\s*\((.*?)\);'
        tables = re.findall(table_pattern, sql_content, re.IGNORECASE | re.DOTALL)

        print(f"找到 {len(tables)} 个表定义，按表分块处理")

        for table_name, table_def in tables:
            try:
                # 每个表作为一个独立的分块
                entity_doc = self._parse_single_table(table_name, table_def, sql_content)
                if entity_doc:
                    entity_docs.append(entity_doc)
                    print(f"  - 处理表 {table_name}: {len(entity_doc.get('fields', []))} 个字段")
            except Exception as e:
                print(f"  - 处理表 {table_name} 失败: {e}")
                continue

        return entity_docs

    def _parse_single_table(self, table_name: str, table_def: str, sql_content: str) -> Dict:
        """解析单个数据表"""
        # 解析字段定义
        fields = []

        # 分行处理表定义
        lines = table_def.split('\n')
        for line in lines:
            line = line.strip()
            if not line or line.startswith('--') or 'CONSTRAINT' in line.upper() or 'PRIMARY KEY' in line.upper():
                continue

            # 移除末尾的逗号
            line = line.rstrip(',')

            # 提取字段名和类型
            field_match = re.match(r'"?(\w+)"?\s+([^,\s]+)', line)
            if field_match:
                field_name = field_match.group(1)
                field_type = field_match.group(2)

                fields.append({
                    'name': field_name,
                    'type': field_type,
                    'comment': ''  # 注释在后面单独处理
                })

        # 查找表和字段的注释
        table_comment = ""
        table_comment_match = re.search(rf'COMMENT\s+ON\s+TABLE\s+"?{table_name}"?\s+IS\s+[\'"]([^\'"]+)[\'"]', sql_content, re.IGNORECASE)
        if table_comment_match:
            table_comment = table_comment_match.group(1)

        # 查找字段注释
        for field in fields:
            comment_match = re.search(rf'COMMENT\s+ON\s+COLUMN\s+"?{table_name}"?\."?{field["name"]}"?\s+IS\s+[\'"]([^\'"]+)[\'"]', sql_content, re.IGNORECASE)
            if comment_match:
                field['comment'] = comment_match.group(1)

        # 构建实体文档
        field_descriptions = []
        for field in fields:
            desc = f"{field['name']}"
            if field['comment']:
                desc += f"({field['comment']})"
            if field['type']:
                desc += f"[{field['type']}]"
            field_descriptions.append(desc)

        content = f"表名: {table_name}"
        if table_comment:
            content += f"({table_comment})"
        content += f"\n字段: " + ", ".join(field_descriptions)

        return {
            'table_name': table_name,
            'table_comment': table_comment,
            'fields': fields,
            'content': content,
            'type': 'entity',
            'source': 'sql_file'
        }

    def _parse_sql_tables_legacy(self, sql_content: str) -> List[Dict]:
        """原有的SQL解析方式（兼容性保留）"""
        entity_docs = []

        # 提取CREATE TABLE语句 - 修改正则表达式以适应PostgreSQL语法
        table_pattern = r'CREATE\s+TABLE\s+(?:IF\s+NOT\s+EXISTS\s+)?"?(\w+)"?\s*\((.*?)\);'
        tables = re.findall(table_pattern, sql_content, re.IGNORECASE | re.DOTALL)

        print(f"找到 {len(tables)} 个表定义")

        for table_name, table_def in tables:
            entity_doc = self._parse_single_table(table_name, table_def, sql_content)
            if entity_doc:
                entity_docs.append(entity_doc)
                print(f"  - 提取字段 {len(entity_doc.get('fields', []))} 个")

        return entity_docs

    def _parse_json_file(self, file_path: str) -> List[Dict]:
        """解析JSON格式的数据库设计文件（如PDMA格式）"""
        entity_docs = []

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                json_data = json.load(f)

            print(f"JSON文件大小: {len(json.dumps(json_data))} 字符")

            # 解析entities数组
            entities = json_data.get('entities', [])
            print(f"找到 {len(entities)} 个实体定义")

            for entity in entities:
                table_name = entity.get('defKey', '')
                table_comment = entity.get('defName', '')
                entity_comment = entity.get('comment', '')

                if not table_name:
                    continue

                #print(f"解析实体: {table_name}")

                # 解析字段信息
                fields = []
                entity_fields = entity.get('fields', [])

                for field in entity_fields:
                    field_name = field.get('defKey', '')
                    field_comment = field.get('defName', '')
                    field_type = field.get('type', '')
                    field_len = field.get('len', '')
                    field_scale = field.get('scale', '')
                    field_primary = field.get('primaryKey', False)
                    field_not_null = field.get('notNull', False)
                    field_auto_inc = field.get('autoIncrement', False)
                    field_default = field.get('defaultValue', '')
                    additional_comment = field.get('comment', '')

                    if field_name:
                        # 组合完整的字段注释
                        full_comment = field_comment
                        if additional_comment:
                            full_comment = f"{field_comment};{additional_comment}" if field_comment else additional_comment

                        fields.append({
                            'name': field_name,
                            'type': field_type,
                            'len': field_len,
                            'scale': field_scale,
                            'primaryKey': field_primary,
                            'notNull': field_not_null,
                            'autoIncrement': field_auto_inc,
                            'defaultValue': field_default,
                            'comment': full_comment
                        })

                # 构建实体文档
                field_descriptions = []
                for field in fields:
                    desc = f"{field['name']}"
                    if field['comment']:
                        desc += f"({field['comment']})"
                    if field['type']:
                        type_desc = field['type']
                        if field['len']:
                            type_desc += f"({field['len']}"
                            if field['scale']:
                                type_desc += f",{field['scale']}"
                            type_desc += ")"
                        desc += f"[{type_desc}]"

                    # 添加约束信息
                    constraints = []
                    if field['primaryKey']:
                        constraints.append('PK')
                    if field['notNull']:
                        constraints.append('NOT NULL')
                    if field['autoIncrement']:
                        constraints.append('AUTO_INCREMENT')
                    if field['defaultValue']:
                        constraints.append(f"DEFAULT:{field['defaultValue']}")

                    if constraints:
                        desc += f"<{','.join(constraints)}>"

                    field_descriptions.append(desc)

                # 组合表注释
                full_table_comment = table_comment
                if entity_comment:
                    full_table_comment = f"{table_comment};{entity_comment}" if table_comment else entity_comment

                content = f"表名: {table_name}"
                if full_table_comment:
                    content += f"({full_table_comment})"
                content += f"\n字段: " + ", ".join(field_descriptions)

                entity_docs.append({
                    'table_name': table_name,
                    'table_comment': full_table_comment,
                    'entity_comment': entity_comment,
                    'fields': fields,
                    'content': content,
                    'type': 'entity',
                    'source': 'json_file'
                })

                #print(f"  - 提取字段 {len(fields)} 个")

        except Exception as e:
            print(f"解析JSON文件失败: {e}")
            import traceback
            traceback.print_exc()

        return entity_docs
    
    def search_knowledge(self, query: str, search_type: str = 'both') -> Dict[str, List[Dict]]:
        """
        搜索知识库，返回分离的用户手册和SQL结果

        Args:
            query: 查询文本
            search_type: 搜索类型 ('function', 'entity', 'both')

        Returns:
            包含manual_results和sql_results的字典
        """
        if not self.enabled:
            return {'manual_results': [], 'sql_results': []}

        try:
            if self.use_chromadb:
                return self._search_chromadb(query, search_type)
            else:
                return self._search_tfidf(query, search_type)
        except Exception as e:
            print(f"知识库搜索失败: {e}")
            return {'manual_results': [], 'sql_results': []}

    def _search_chromadb(self, query: str, search_type: str) -> Dict[str, List[Dict]]:
        """使用ChromaDB搜索"""
        if not self.embedding_model:
            return {'manual_results': [], 'sql_results': []}

        # 向量化查询
        query_embedding = self.embedding_model.encode([query])

        manual_results = []
        sql_results = []

        # 搜索功能文档（用户手册）
        if search_type in ['function', 'both'] and self.function_collection:
            function_results = self.function_collection.query(
                query_embeddings=query_embedding.tolist(),
                n_results=self.top_k
            )

            for doc_id, distance, document, metadata in zip(
                function_results['ids'][0],
                function_results['distances'][0],
                function_results['documents'][0],
                function_results['metadatas'][0]
            ):
                similarity = 1 - distance  # 转换为相似度
                if similarity >= self.similarity_threshold:
                    manual_results.append({
                        'id': doc_id,
                        'title': metadata.get('title', 'Unknown'),
                        'content': document,
                        'similarity': similarity,
                        'type': metadata.get('type', 'function'),
                        'source': metadata.get('source', 'user_manual')
                    })

        # 搜索实体文档（SQL/JSON）
        if search_type in ['entity', 'both'] and self.entity_collection:
            entity_results = self.entity_collection.query(
                query_embeddings=query_embedding.tolist(),
                n_results=self.top_k
            )

            for doc_id, distance, document, metadata in zip(
                entity_results['ids'][0],
                entity_results['distances'][0],
                entity_results['documents'][0],
                entity_results['metadatas'][0]
            ):
                similarity = 1 - distance  # 转换为相似度
                if similarity >= self.similarity_threshold:
                    sql_results.append({
                        'id': doc_id,
                        'table_name': metadata.get('table_name', 'Unknown'),
                        'table_comment': metadata.get('table_comment', ''),
                        'content': document,
                        'similarity': similarity,
                        'type': metadata.get('type', 'entity'),
                        'source': metadata.get('source', 'sql_file')
                    })

        # 按相似度排序
        manual_results.sort(key=lambda x: x['similarity'], reverse=True)
        sql_results.sort(key=lambda x: x['similarity'], reverse=True)

        return {
            'manual_results': manual_results[:self.top_k],
            'sql_results': sql_results[:self.top_k]
        }

    def _search_tfidf(self, query: str, search_type: str) -> Dict[str, List[Dict]]:
        """使用TF-IDF搜索"""
        if not self.vectorizer:
            return {'manual_results': [], 'sql_results': []}

        # 向量化查询
        query_vector = self.vectorizer.transform([query])

        manual_results = []
        sql_results = []

        # 搜索功能文档
        if search_type in ['function', 'both'] and self.function_vectors is not None:
            similarities = cosine_similarity(query_vector, self.function_vectors).flatten()
            for i, sim in enumerate(similarities):
                if sim >= self.similarity_threshold:
                    doc = self.function_docs[i].copy()
                    manual_results.append({
                        'id': f"func_{i}",
                        'title': doc.get('title', 'Unknown'),
                        'content': doc['content'],
                        'similarity': float(sim),
                        'type': doc.get('type', 'function'),
                        'source': doc.get('source', 'user_manual')
                    })

        # 搜索实体文档
        if search_type in ['entity', 'both'] and self.entity_vectors is not None:
            similarities = cosine_similarity(query_vector, self.entity_vectors).flatten()
            for i, sim in enumerate(similarities):
                if sim >= self.similarity_threshold:
                    doc = self.entity_docs[i].copy()
                    sql_results.append({
                        'id': f"entity_{i}",
                        'table_name': doc.get('table_name', 'Unknown'),
                        'table_comment': doc.get('table_comment', ''),
                        'content': doc['content'],
                        'similarity': float(sim),
                        'type': doc.get('type', 'entity'),
                        'source': doc.get('source', 'sql_file')
                    })

        # 按相似度排序
        manual_results.sort(key=lambda x: x['similarity'], reverse=True)
        sql_results.sort(key=lambda x: x['similarity'], reverse=True)

        return {
            'manual_results': manual_results[:self.top_k],
            'sql_results': sql_results[:self.top_k]
        }
    

    
    def get_context_for_module(self, level_1: str, level_2: str, level_3: str,
                              function_desc: str) -> str:
        """
        为特定模块获取知识库上下文

        Args:
            level_1: 一级模块名称
            level_2: 二级模块名称
            level_3: 三级模块名称
            function_desc: 功能描述

        Returns:
            格式化的上下文字符串
        """
        if not self.enabled:
            return ""

        # 构建查询文本
        query_parts = [level_1, level_2, level_3, function_desc]
        query = " ".join([part for part in query_parts if part and str(part).strip()])

        # 搜索相关知识
        search_results = self.search_knowledge(query)
        manual_results = search_results['manual_results']
        sql_results = search_results['sql_results']

        if not manual_results and not sql_results:
            return ""

        # 格式化上下文
        context_parts = ["## 知识库检索上下文\n"]

        # 用户手册相关功能说明
        if manual_results:
            context_parts.append("### 用户手册相关功能说明:")
            for i, result in enumerate(manual_results[:3], 1):
                context_parts.append(f"{i}. {result['title']}")
                context_parts.append(f"   {result['content'][:200]}...")
                context_parts.append(f"   (相似度: {result['similarity']:.3f})")
                context_parts.append("")

        # SQL/JSON相关数据实体
        if sql_results:
            context_parts.append("### 数据库相关实体:")
            for i, result in enumerate(sql_results[:3], 1):
                context_parts.append(f"{i}. {result['table_name']}")
                if result['table_comment']:
                    context_parts.append(f"   说明: {result['table_comment']}")
                context_parts.append(f"   内容: {result['content'][:150]}...")
                context_parts.append(f"   (相似度: {result['similarity']:.3f})")
                context_parts.append("")

        return "\n".join(context_parts)


# 全局知识库实例
knowledge_base = KnowledgeBase()
